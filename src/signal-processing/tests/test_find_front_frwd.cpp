#include <iostream>
#include <vector>
#include <stdexcept>
#include <cmath>
#include "../find_front_frwd.h"

using IQVideoProcessor::SignalProcessing::FindFrontFrwd;

static std::vector<float> makeStep(size_t total, size_t stepIndex, float low, float high) {
  std::vector<float> v(total, low);
  for (size_t i = stepIndex; i < total; ++i) v[i] = high;
  return v;
}

static void buildPadded(const std::vector<float>& effective, size_t leftPad, size_t rightPad, std::vector<float>& out) {
  out.resize(leftPad + effective.size() + rightPad);
  std::fill(out.begin(), out.begin() + leftPad, effective.front());
  std::copy(effective.begin(), effective.end(), out.begin() + leftPad);
  std::fill(out.begin() + leftPad + effective.size(), out.end(), effective.back());
}

void test_basic_rising() {
  const size_t total = 256, leftPad = 64, rightPad = 64;
  auto effective = makeStep(total, 100, -0.2f, 0.8f);
  std::vector<float> padded; buildPadded(effective, leftPad, rightPad, padded);

  std::vector<float> ring(64);
  FindFrontFrwd<float> finder(padded.data() + leftPad, total, ring);
  float pos = 0.0f; bool found = false;
  bool ok = finder(true, +1, 0.0f, 0.0f, 9.0f, 33.0f, 0.3f, pos, found);
  if (!ok || !found || !(pos > 80 && pos < 120)) throw std::runtime_error("rising failed");
  std::cout << "✓ basic rising at " << pos << std::endl;
}

void test_basic_falling() {
  const size_t total = 256, leftPad = 64, rightPad = 64;
  auto effective = makeStep(total, 120, 0.8f, -0.2f);
  std::vector<float> padded; buildPadded(effective, leftPad, rightPad, padded);

  std::vector<float> ring(64);
  FindFrontFrwd<float> finder(padded.data() + leftPad, total, ring);
  float pos = 0.0f; bool found = false;
  bool ok = finder(true, -1, 0.0f, 0.0f, 9.0f, 33.0f, 0.3f, pos, found);
  if (!ok || !found || !(pos > 100 && pos < 140)) throw std::runtime_error("falling failed");
  std::cout << "✓ basic falling at " << pos << std::endl;
}

void test_small_buffers() {
  std::vector<float> ring(8);

  // empty
  {
    std::vector<float> v; FindFrontFrwd<float> finder(v.data(), 0, ring);
    float pos = 0.0f; bool found = false;
    bool ok = finder(true, 0, 0.0f, 0.0f, 1.0f, 1.0f, 0.1f, pos, found);
    if (ok || found) throw std::runtime_error("empty should not detect");
  }
  // single element
  {
    std::vector<float> v = {0.0f}; FindFrontFrwd<float> finder(v.data(), 1, ring);
    float pos = 0.0f; bool found = false;
    bool ok = finder(true, 0, 0.0f, 0.0f, 1.0f, 1.0f, 0.1f, pos, found);
    if (!ok || found) throw std::runtime_error("single should not detect");
  }
  // two elements, insufficient delta
  {
    std::vector<float> v = {0.0f, 0.05f}; FindFrontFrwd<float> finder(v.data(), 2, ring);
    float pos = 0.0f; bool found = false;
    bool ok = finder(true, 0, 0.0f, 0.0f, 1.0f, 1.0f, 0.2f, pos, found);
    if (!ok || found) throw std::runtime_error("small insufficient delta");
  }
  std::cout << "✓ small buffers ok" << std::endl;
}

void test_boundaries() {
  const size_t total = 64, pad = 16;
  // front at start: left pad MUST be low to create a rising step at index 0
  {
    auto effective = makeStep(total, 0, -0.5f, 0.5f); // entire effective is high
    std::vector<float> padded(pad + total + pad);
    std::fill(padded.begin(), padded.begin() + pad, -0.5f); // left low
    std::copy(effective.begin(), effective.end(), padded.begin() + pad);
    std::fill(padded.begin() + pad + total, padded.end(), 0.5f); // right high

    std::vector<float> ring(33);
    FindFrontFrwd<float> finder(padded.data() + pad, total, ring);
    float pos = 0.0f; bool found = false;
    bool ok = finder(true, +1, 0.0f, 0.0f, 9.0f, 33.0f, 0.2f, pos, found);
    if (!ok || !found) throw std::runtime_error("front at start not found");
  }
  // front at end: right pad MUST be high; left is low
  {
    auto effective = makeStep(total, total - 1, -0.5f, 0.5f); // last index becomes high
    std::vector<float> padded(pad + total + pad);
    std::fill(padded.begin(), padded.begin() + pad, -0.5f); // left low
    std::copy(effective.begin(), effective.end(), padded.begin() + pad);
    std::fill(padded.begin() + pad + total, padded.end(), 0.5f); // right high

    std::vector<float> ring(33);
    FindFrontFrwd<float> finder(padded.data() + pad, total, ring);
    float pos = 0.0f; bool found = false;
    bool ok = finder(true, +1, 0.0f, 0.0f, 9.0f, 33.0f, 0.2f, pos, found);
    if (!ok || !found) throw std::runtime_error("front at end not found");
  }
  std::cout << "✓ boundary conditions ok" << std::endl;
}

void test_parameter_combinations() {
  const size_t total = 256, pad = 64;
  auto effective = makeStep(total, 80, -0.2f, 0.8f);
  std::vector<float> padded; buildPadded(effective, pad, pad, padded);

  for (size_t ave : {1ul, 3ul, 5ul, 9ul, 17ul}) {
    for (size_t thr : {1ul, 5ul, 17ul, 33ul}) {
      for (float delta : {0.1f, 0.2f, 0.4f}) {
        std::vector<float> ring(thr);
        FindFrontFrwd<float> finder(padded.data() + pad, total, ring);
        float pos = 0.0f; bool found = false;
        (void)finder(true, +1, 0.0f, 0.0f, static_cast<float>(ave), static_cast<float>(thr), delta, pos, found);
      }
    }
  }
  std::cout << "✓ parameter combinations exercised" << std::endl;
}

void test_no_detection_and_front_type() {
  const size_t total = 128, pad = 32;
  auto effective = makeStep(total, 64, -0.1f, 0.2f); // small step
  std::vector<float> padded; buildPadded(effective, pad, pad, padded);

  std::vector<float> ring(33);
  FindFrontFrwd<float> finder(padded.data() + pad, total, ring);

  // High threshold: no detection
  {
    float pos = 0.0f; bool found = false;
    bool ok = finder(true, 0, 0.0f, 0.0f, 9.0f, 33.0f, 1.0f, pos, found);
    if (!ok || found) throw std::runtime_error("unexpected detection with high threshold");
  }

  // Wrong front type
  {
    float pos = 0.0f; bool found = false;
    bool ok = finder(true, -1, 0.0f, 0.0f, 9.0f, 33.0f, 0.05f, pos, found); // rising but asking falling
    if (!ok || found) throw std::runtime_error("unexpected detection with wrong front type");
  }

  std::cout << "✓ no-detection scenarios ok" << std::endl;
}

void test_double_precision_instantiation() {
  std::vector<double> ringD(33);
  std::vector<double> data(100, 0.0);
  FindFrontFrwd<double> finderD(data.data(), data.size(), ringD);
  // Just ensure template compiles and can be called with valid inputs
  double posD = 0.0; bool foundD = false;
  bool ok = finderD(true, +1, 0.0, 0.0, 9.0, 33.0, 0.3, posD, foundD);
  (void)ok; (void)foundD;
  std::cout << "✓ double precision instantiation compiled" << std::endl;
}

int main() {
  try {
    test_basic_rising();
    test_basic_falling();
    test_small_buffers();
    test_boundaries();
    test_parameter_combinations();
    test_no_detection_and_front_type();
    test_double_precision_instantiation();
    std::cout << "\n🎉 All FindFrontFrwd tests PASSED!" << std::endl;
    return 0;
  } catch (const std::exception& e) {
    std::cerr << "❌ FindFrontFrwd test failed: " << e.what() << std::endl;
    return 1;
  }
}
