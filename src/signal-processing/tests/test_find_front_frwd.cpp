#include <iostream>
#include <vector>
#include <stdexcept>
#include <cmath>
#include <iomanip>
#include <sstream>
#include <algorithm>
#include "../find_front_frwd.h"

using IQVideoProcessor::SignalProcessing::FindFrontFrwd;

// Test result tracking
struct TestResult {
  std::string name;
  bool passed;
  std::string error;
  double frontPos;
  bool frontFound;
};

std::vector<TestResult> testResults;

// Helper functions
static std::vector<float> makeStep(size_t total, size_t stepIndex, float low, float high) {
  std::vector<float> v(total, low);
  for (size_t i = stepIndex; i < total; ++i) v[i] = high;
  return v;
}

static void buildPadded(const std::vector<float>& effective, size_t leftPad, size_t rightPad, std::vector<float>& out) {
  out.resize(leftPad + effective.size() + rightPad);
  std::fill(out.begin(), out.begin() + leftPad, effective.front());
  std::copy(effective.begin(), effective.end(), out.begin() + leftPad);
  std::fill(out.begin() + leftPad + effective.size(), out.end(), effective.back());
}



// Test execution helper
#define RUN_TEST(test_func) do { \
  try { \
    test_func(); \
    testResults.push_back({#test_func, true, "", 0.0, false}); \
  } catch (const std::exception& e) { \
    testResults.push_back({#test_func, false, e.what(), 0.0, false}); \
  } \
} while(0)

void test_basic_rising() {
  const size_t total = 256, leftPad = 64, rightPad = 64;
  auto effective = makeStep(total, 100, -0.2f, 0.8f);
  std::vector<float> padded; buildPadded(effective, leftPad, rightPad, padded);

  std::vector<float> ring(64);
  FindFrontFrwd<float> finder(padded.data() + leftPad, total, ring);
  float pos = 0.0f; bool found = false;
  bool ok = finder(true, +1, 0.0f, 0.0f, 9.0f, 33.0f, 0.3f, pos, found);
  if (!ok || !found || !(pos > 80 && pos < 120)) throw std::runtime_error("rising failed");
  std::cout << "✓ basic rising at " << pos << std::endl;
}

void test_basic_falling() {
  const size_t total = 256, leftPad = 64, rightPad = 64;
  auto effective = makeStep(total, 120, 0.8f, -0.2f);
  std::vector<float> padded; buildPadded(effective, leftPad, rightPad, padded);

  std::vector<float> ring(64);
  FindFrontFrwd<float> finder(padded.data() + leftPad, total, ring);
  float pos = 0.0f; bool found = false;
  bool ok = finder(true, -1, 0.0f, 0.0f, 9.0f, 33.0f, 0.3f, pos, found);
  if (!ok || !found || !(pos > 100 && pos < 140)) throw std::runtime_error("falling failed");
  std::cout << "✓ basic falling at " << pos << std::endl;
}

void test_small_buffers() {
  std::vector<float> ring(8);

  // empty
  {
    std::vector<float> v; FindFrontFrwd<float> finder(v.data(), 0, ring);
    float pos = 0.0f; bool found = false;
    bool ok = finder(true, 0, 0.0f, 0.0f, 1.0f, 1.0f, 0.1f, pos, found);
    if (!ok || found) throw std::runtime_error("empty should return ok=true, found=false");
  }
  // single element
  {
    std::vector<float> v = {0.0f}; FindFrontFrwd<float> finder(v.data(), 1, ring);
    float pos = 0.0f; bool found = false;
    bool ok = finder(true, 0, 0.0f, 0.0f, 1.0f, 1.0f, 0.1f, pos, found);
    if (!ok || found) throw std::runtime_error("single should not detect");
  }
  // two elements, insufficient delta
  {
    std::vector<float> v = {0.0f, 0.05f}; FindFrontFrwd<float> finder(v.data(), 2, ring);
    float pos = 0.0f; bool found = false;
    bool ok = finder(true, 0, 0.0f, 0.0f, 1.0f, 1.0f, 0.2f, pos, found);
    if (!ok || found) throw std::runtime_error("small insufficient delta");
  }
  std::cout << "✓ small buffers ok" << std::endl;
}

void test_boundaries() {
  const size_t total = 64, pad = 16;
  // front at start: left pad MUST be low to create a rising step at index 0
  {
    auto effective = makeStep(total, 0, -0.5f, 0.5f); // entire effective is high
    std::vector<float> padded(pad + total + pad);
    std::fill(padded.begin(), padded.begin() + pad, -0.5f); // left low
    std::copy(effective.begin(), effective.end(), padded.begin() + pad);
    std::fill(padded.begin() + pad + total, padded.end(), 0.5f); // right high

    std::vector<float> ring(33);
    FindFrontFrwd<float> finder(padded.data() + pad, total, ring);
    float pos = 0.0f; bool found = false;
    bool ok = finder(true, +1, 0.0f, 0.0f, 9.0f, 33.0f, 0.2f, pos, found);
    if (!ok || !found) throw std::runtime_error("front at start not found");
  }
  // front at end: right pad MUST be high; left is low
  {
    auto effective = makeStep(total, total - 1, -0.5f, 0.5f); // last index becomes high
    std::vector<float> padded(pad + total + pad);
    std::fill(padded.begin(), padded.begin() + pad, -0.5f); // left low
    std::copy(effective.begin(), effective.end(), padded.begin() + pad);
    std::fill(padded.begin() + pad + total, padded.end(), 0.5f); // right high

    // Test with aveSize=1 (no averaging) - this should work
    {
      std::vector<float> ring(5);
      FindFrontFrwd<float> finder(padded.data() + pad, total, ring);
      float pos = 0.0f; bool found = false;
      bool ok = finder(true, +1, 0.0f, 0.0f, 1.0f, 5.0f, 0.2f, pos, found);
      if (!ok || !found) throw std::runtime_error("front at end not found with aveSize=1");
    }

    // Test with aveSize=9 (moving average) - boundary limitation expected
    {
      std::vector<float> ring(33);
      FindFrontFrwd<float> finder(padded.data() + pad, total, ring);
      float pos = 0.0f; bool found = false;
      bool ok = finder(true, +1, 0.0f, 0.0f, 9.0f, 33.0f, 0.2f, pos, found);
      // This is expected to fail due to moving average boundary limitation
      if (!ok) throw std::runtime_error("algorithm execution failed");
      // Note: found=false is acceptable here due to boundary limitation
    }
  }
  std::cout << "✓ boundary conditions ok" << std::endl;
}

void test_parameter_combinations() {
  const size_t total = 256, pad = 64;
  auto effective = makeStep(total, 80, -0.2f, 0.8f);
  std::vector<float> padded; buildPadded(effective, pad, pad, padded);

  for (size_t ave : {1ul, 3ul, 5ul, 9ul, 17ul}) {
    for (size_t thr : {1ul, 5ul, 17ul, 33ul}) {
      for (float delta : {0.1f, 0.2f, 0.4f}) {
        std::vector<float> ring(thr);
        FindFrontFrwd<float> finder(padded.data() + pad, total, ring);
        float pos = 0.0f; bool found = false;
        (void)finder(true, +1, 0.0f, 0.0f, static_cast<float>(ave), static_cast<float>(thr), delta, pos, found);
      }
    }
  }
  std::cout << "✓ parameter combinations exercised" << std::endl;
}

void test_no_detection_and_front_type() {
  const size_t total = 128, pad = 32;
  auto effective = makeStep(total, 64, -0.1f, 0.2f); // small step
  std::vector<float> padded; buildPadded(effective, pad, pad, padded);

  std::vector<float> ring(33);
  FindFrontFrwd<float> finder(padded.data() + pad, total, ring);

  // High threshold: no detection
  {
    float pos = 0.0f; bool found = false;
    bool ok = finder(true, 0, 0.0f, 0.0f, 9.0f, 33.0f, 1.0f, pos, found);
    if (!ok || found) throw std::runtime_error("unexpected detection with high threshold");
  }

  // Wrong front type
  {
    float pos = 0.0f; bool found = false;
    bool ok = finder(true, -1, 0.0f, 0.0f, 9.0f, 33.0f, 0.05f, pos, found); // rising but asking falling
    if (!ok || found) throw std::runtime_error("unexpected detection with wrong front type");
  }

  std::cout << "✓ no-detection scenarios ok" << std::endl;
}

void test_double_precision_instantiation() {
  std::vector<double> ringD(33);
  std::vector<double> data(100, 0.0);
  FindFrontFrwd<double> finderD(data.data(), data.size(), ringD);
  // Just ensure template compiles and can be called with valid inputs
  double posD = 0.0; bool foundD = false;
  bool ok = finderD(true, +1, 0.0, 0.0, 9.0, 33.0, 0.3, posD, foundD);
  (void)ok; (void)foundD;
  std::cout << "✓ double precision instantiation compiled" << std::endl;
}

// ============================================================================
// COMPREHENSIVE RING BUFFER VALIDATION TESTS
// ============================================================================

void test_ring_buffer_pre_fill_validation() {
  const size_t total = 128, pad = 32;
  auto effective = makeStep(total, 64, 0.0f, 1.0f);
  std::vector<float> padded; buildPadded(effective, pad, pad, padded);

  // Test different ring buffer sizes and average sizes
  for (size_t ringSize : {1, 3, 5, 9, 17, 33, 65}) {
    for (size_t aveSize : {1, 3, 5, 9, 17}) {
      std::vector<float> ring(ringSize);
      FindFrontFrwd<float> finder(padded.data() + pad, total, ring);
      float pos = 0.0f; bool found = false;

      // Should handle ring buffer initialization without crashing
      bool ok = finder(true, +1, 0.0f, 0.0f, static_cast<float>(aveSize),
                      static_cast<float>(ringSize), 0.3f, pos, found);

      if (ringSize > total) {
        // Ring buffer larger than data should fail gracefully
        if (ok) throw std::runtime_error("Should fail when ring > data size");
      }
    }
  }
  std::cout << "✓ ring buffer pre-fill validation" << std::endl;
}

void test_ring_buffer_wraparound_scenarios() {
  const size_t total = 256, pad = 64;

  // Create data with multiple fronts to test wraparound
  std::vector<float> effective(total, 0.0f);
  for (size_t i = 50; i < 60; ++i) effective[i] = 1.0f;   // First front
  for (size_t i = 100; i < 110; ++i) effective[i] = 2.0f; // Second front
  for (size_t i = 150; i < 160; ++i) effective[i] = 3.0f; // Third front

  std::vector<float> padded; buildPadded(effective, pad, pad, padded);

  // Test with small ring buffers that will definitely wrap around
  for (size_t ringSize : {3, 5, 7, 11}) {
    std::vector<float> ring(ringSize);
    FindFrontFrwd<float> finder(padded.data() + pad, total, ring);
    float pos = 0.0f; bool found = false;

    bool ok = finder(true, +1, 0.0f, 0.0f, 3.0f, static_cast<float>(ringSize), 0.2f, pos, found);

    if (!ok) throw std::runtime_error("Ring buffer wraparound failed");
    // Should find at least one front
    if (!found) throw std::runtime_error("No front found in wraparound test");
  }
  std::cout << "✓ ring buffer wraparound scenarios" << std::endl;
}

void test_ring_buffer_circular_index_management() {
  const size_t total = 64, pad = 16;
  auto effective = makeStep(total, 32, -1.0f, 1.0f);
  std::vector<float> padded; buildPadded(effective, pad, pad, padded);

  // Test edge case: ring size = 1 (maximum wraparound frequency)
  {
    std::vector<float> ring(1);
    FindFrontFrwd<float> finder(padded.data() + pad, total, ring);
    float pos = 0.0f; bool found = false;
    bool ok = finder(true, +1, 0.0f, 0.0f, 1.0f, 1.0f, 0.5f, pos, found);
    if (!ok) throw std::runtime_error("Ring size 1 failed");
  }

  // Test edge case: ring size = 2 (alternating indices)
  {
    std::vector<float> ring(2);
    FindFrontFrwd<float> finder(padded.data() + pad, total, ring);
    float pos = 0.0f; bool found = false;
    bool ok = finder(true, +1, 0.0f, 0.0f, 1.0f, 2.0f, 0.5f, pos, found);
    if (!ok) throw std::runtime_error("Ring size 2 failed");
  }

  std::cout << "✓ ring buffer circular index management" << std::endl;
}

// ============================================================================
// COMPREHENSIVE EDGE CASE TESTS
// ============================================================================

void test_extreme_edge_cases() {
  // Test with maximum/minimum float values
  {
    std::vector<float> data = {-1e6f, 1e6f, -1e6f, 1e6f};
    std::vector<float> ring(2);
    FindFrontFrwd<float> finder(data.data(), data.size(), ring);
    float pos = 0.0f; bool found = false;
    bool ok = finder(true, +1, 0.0f, 0.0f, 1.0f, 2.0f, 1e5f, pos, found);
    if (!ok) throw std::runtime_error("Extreme values test failed");
  }

  // Test with very small deltas
  {
    std::vector<float> data = {0.0f, 1e-6f, 2e-6f, 3e-6f};
    std::vector<float> ring(2);
    FindFrontFrwd<float> finder(data.data(), data.size(), ring);
    float pos = 0.0f; bool found = false;
    bool ok = finder(true, +1, 0.0f, 0.0f, 1.0f, 2.0f, 5e-7f, pos, found);
    if (!ok) throw std::runtime_error("Small delta test failed");
  }

  std::cout << "✓ extreme edge cases" << std::endl;
}

void test_position_accuracy_verification() {
  const size_t total = 1000, pad = 100;

  // Test precise position calculation for known step locations
  for (size_t stepPos : {100, 250, 500, 750, 900}) {
    auto effective = makeStep(total, stepPos, 0.0f, 1.0f);
    std::vector<float> padded; buildPadded(effective, pad, pad, padded);

    std::vector<float> ring(33);
    FindFrontFrwd<float> finder(padded.data() + pad, total, ring);
    float pos = 0.0f; bool found = false;

    bool ok = finder(true, +1, 0.0f, 0.0f, 9.0f, 33.0f, 0.2f, pos, found);

    if (!ok || !found) {
      std::ostringstream oss;
      oss << "Position test failed for step at " << stepPos;
      throw std::runtime_error(oss.str());
    }

    // Verify position is reasonably close to expected
    float expectedPos = static_cast<float>(stepPos);
    float tolerance = 20.0f; // Allow some tolerance due to averaging
    if (std::abs(pos - expectedPos) > tolerance) {
      std::ostringstream oss;
      oss << "Position accuracy failed: expected ~" << expectedPos
          << ", got " << pos << " (diff: " << std::abs(pos - expectedPos) << ")";
      throw std::runtime_error(oss.str());
    }
  }

  std::cout << "✓ position accuracy verification" << std::endl;
}

void test_index_access_bounds_checking() {
  const size_t total = 64, pad = 32;
  auto effective = makeStep(total, 32, 0.0f, 1.0f);
  std::vector<float> padded; buildPadded(effective, pad, pad, padded);

  // Test various combinations that might cause out-of-bounds access
  for (size_t aveSize : {1, 3, 9, 17}) {
    for (size_t thresholdSize : {1, 5, 17, 31}) {
      if (thresholdSize > total) continue;

      std::vector<float> ring(thresholdSize);
      FindFrontFrwd<float> finder(padded.data() + pad, total, ring);
      float pos = 0.0f; bool found = false;

      // This should not crash or access invalid memory
      bool ok = finder(true, +1, 0.0f, 0.0f, static_cast<float>(aveSize),
                      static_cast<float>(thresholdSize), 0.3f, pos, found);

      // We don't care about the result, just that it doesn't crash
      (void)ok; (void)found; (void)pos;
    }
  }

  std::cout << "✓ index access bounds checking" << std::endl;
}

void test_stress_testing_scenarios() {
  // High-volume test with large data
  {
    const size_t total = 10000, pad = 1000;
    auto effective = makeStep(total, 5000, -0.5f, 0.5f);
    std::vector<float> padded; buildPadded(effective, pad, pad, padded);

    std::vector<float> ring(100);
    FindFrontFrwd<float> finder(padded.data() + pad, total, ring);
    float pos = 0.0f; bool found = false;

    bool ok = finder(true, +1, 0.0f, 0.0f, 25.0f, 100.0f, 0.2f, pos, found);
    if (!ok) throw std::runtime_error("High-volume stress test failed");
  }

  // Multiple rapid fronts
  {
    const size_t total = 200, pad = 50;
    std::vector<float> effective(total, 0.0f);
    for (size_t i = 0; i < total; i += 10) {
      if ((i / 10) % 2 == 0) effective[i] = 1.0f;
    }
    std::vector<float> padded; buildPadded(effective, pad, pad, padded);

    std::vector<float> ring(5);
    FindFrontFrwd<float> finder(padded.data() + pad, total, ring);
    float pos = 0.0f; bool found = false;

    bool ok = finder(true, +1, 0.0f, 0.0f, 1.0f, 5.0f, 0.2f, pos, found);
    if (!ok) throw std::runtime_error("Rapid fronts stress test failed");
  }

  std::cout << "✓ stress testing scenarios" << std::endl;
}

int main() {
  std::cout << "FindFrontFrwd Comprehensive Test Suite" << std::endl;
  std::cout << "======================================" << std::endl;

  // Run original tests
  RUN_TEST(test_basic_rising);
  RUN_TEST(test_basic_falling);
  RUN_TEST(test_small_buffers);
  RUN_TEST(test_boundaries);
  RUN_TEST(test_parameter_combinations);
  RUN_TEST(test_no_detection_and_front_type);
  RUN_TEST(test_double_precision_instantiation);

  // Run comprehensive new tests
  RUN_TEST(test_ring_buffer_pre_fill_validation);
  RUN_TEST(test_ring_buffer_wraparound_scenarios);
  RUN_TEST(test_ring_buffer_circular_index_management);
  RUN_TEST(test_extreme_edge_cases);
  RUN_TEST(test_position_accuracy_verification);
  RUN_TEST(test_index_access_bounds_checking);
  RUN_TEST(test_stress_testing_scenarios);

  // Print results summary
  std::cout << "\n" << std::string(50, '=') << std::endl;
  std::cout << "TEST RESULTS SUMMARY" << std::endl;
  std::cout << std::string(50, '=') << std::endl;

  size_t passed = 0, failed = 0;
  for (const auto& result : testResults) {
    if (result.passed) {
      std::cout << "✓ " << result.name << std::endl;
      ++passed;
    } else {
      std::cout << "❌ " << result.name << ": " << result.error << std::endl;
      ++failed;
    }
  }

  std::cout << "\nSUMMARY: " << passed << " passed, " << failed << " failed" << std::endl;

  if (failed == 0) {
    std::cout << "\n🎉 All FindFrontFrwd tests PASSED!" << std::endl;
    return 0;
  } else {
    std::cout << "\n❌ " << failed << " test(s) FAILED!" << std::endl;
    return 1;
  }
}
