#include <iostream>
#include <vector>
#include <stdexcept>
#include <cmath>
#include <iomanip>
#include <sstream>
#include <algorithm>
#include "../find_front_frwd.h"

using IQVideoProcessor::SignalProcessing::FindFrontFrwd;

// Test result tracking for front detection scenarios
struct FrontDetectionResult {
  std::string testName;
  bool passed;
  std::string error;
  double expectedPos;
  double actualPos;
  bool frontFound;
};

std::vector<FrontDetectionResult> frontResults;

// Helper functions for signal generation
static std::vector<float> makeStepFunction(size_t total, size_t stepIndex, float low, float high) {
  std::vector<float> v(total, low);
  for (size_t i = stepIndex; i < total; ++i) v[i] = high;
  return v;
}

static std::vector<float> makeNoisySignal(size_t total, float amplitude, unsigned seed = 42) {
  std::vector<float> v(total);
  srand(seed);
  for (size_t i = 0; i < total; ++i) {
    v[i] = amplitude * (2.0f * static_cast<float>(rand()) / RAND_MAX - 1.0f);
  }
  return v;
}

static std::vector<float> addNoise(const std::vector<float>& signal, float noiseLevel, unsigned seed = 123) {
  std::vector<float> noisy = signal;
  srand(seed);
  for (size_t i = 0; i < noisy.size(); ++i) {
    float noise = noiseLevel * (2.0f * static_cast<float>(rand()) / RAND_MAX - 1.0f);
    noisy[i] += noise;
  }
  return noisy;
}

static void buildPaddedSignal(const std::vector<float>& effective, size_t leftPad, size_t rightPad, std::vector<float>& out) {
  out.resize(leftPad + effective.size() + rightPad);
  std::fill(out.begin(), out.begin() + leftPad, effective.front());
  std::copy(effective.begin(), effective.end(), out.begin() + leftPad);
  std::fill(out.begin() + leftPad + effective.size(), out.end(), effective.back());
}

// Test execution helper
#define RUN_FRONT_TEST(test_func) do { \
  try { \
    test_func(); \
    frontResults.push_back({#test_func, true, "", 0.0, 0.0, false}); \
  } catch (const std::exception& e) { \
    frontResults.push_back({#test_func, false, e.what(), 0.0, 0.0, false}); \
  } \
} while(0)

// ============================================================================
// STEP FUNCTION FRONT DETECTION TESTS
// ============================================================================

void test_sharp_step_fronts() {
  const size_t total = 256, pad = 64;
  
  // Test various step positions
  for (size_t stepPos : {50, 100, 150, 200}) {
    auto effective = makeStepFunction(total, stepPos, -1.0f, 1.0f);
    std::vector<float> padded; buildPaddedSignal(effective, pad, pad, padded);
    
    std::vector<float> ring(33);
    FindFrontFrwd<float> finder(padded.data() + pad, total, ring);
    float pos = 0.0f; bool found = false;
    
    bool ok = finder(true, +1, 0.0f, 0.0f, 9.0f, 33.0f, 0.3f, pos, found);
    
    if (!ok || !found) {
      std::ostringstream oss;
      oss << "Sharp step front not detected at position " << stepPos;
      throw std::runtime_error(oss.str());
    }
    
    // Verify position accuracy (allow some tolerance due to averaging)
    float expectedPos = static_cast<float>(stepPos);
    float tolerance = 20.0f;
    if (std::abs(pos - expectedPos) > tolerance) {
      std::ostringstream oss;
      oss << "Position inaccuracy: expected ~" << expectedPos 
          << ", got " << pos << " (diff: " << std::abs(pos - expectedPos) << ")";
      throw std::runtime_error(oss.str());
    }
  }
  
  std::cout << "✓ sharp step fronts detection" << std::endl;
}

void test_small_amplitude_steps() {
  const size_t total = 200, pad = 50;
  
  // Test detection of small amplitude changes
  for (float amplitude : {0.1f, 0.05f, 0.02f, 0.01f}) {
    auto effective = makeStepFunction(total, 100, 0.0f, amplitude);
    std::vector<float> padded; buildPaddedSignal(effective, pad, pad, padded);
    
    std::vector<float> ring(17);
    FindFrontFrwd<float> finder(padded.data() + pad, total, ring);
    float pos = 0.0f; bool found = false;
    
    // Use smaller threshold for small amplitudes
    float threshold = amplitude * 0.5f;
    bool ok = finder(true, +1, 0.0f, 0.0f, 5.0f, 17.0f, threshold, pos, found);
    
    if (!ok) {
      std::ostringstream oss;
      oss << "Algorithm failed for amplitude " << amplitude;
      throw std::runtime_error(oss.str());
    }
    
    // For very small amplitudes, detection might fail - this is expected
    if (amplitude >= 0.05f && !found) {
      std::ostringstream oss;
      oss << "Small amplitude step not detected: " << amplitude;
      throw std::runtime_error(oss.str());
    }
  }
  
  std::cout << "✓ small amplitude steps detection" << std::endl;
}

void test_negative_step_fronts() {
  const size_t total = 180, pad = 40;
  
  // Test falling edge detection
  for (size_t stepPos : {60, 90, 120}) {
    auto effective = makeStepFunction(total, stepPos, 1.0f, -1.0f);
    std::vector<float> padded; buildPaddedSignal(effective, pad, pad, padded);
    
    std::vector<float> ring(25);
    FindFrontFrwd<float> finder(padded.data() + pad, total, ring);
    float pos = 0.0f; bool found = false;
    
    bool ok = finder(true, -1, 0.0f, 0.0f, 7.0f, 25.0f, 0.4f, pos, found);
    
    if (!ok || !found) {
      std::ostringstream oss;
      oss << "Negative step front not detected at position " << stepPos;
      throw std::runtime_error(oss.str());
    }
  }
  
  std::cout << "✓ negative step fronts detection" << std::endl;
}

// ============================================================================
// RAMP FUNCTION FRONT DETECTION TESTS
// ============================================================================

void test_gradual_ramp_fronts() {
  const size_t total = 300, pad = 75;
  
  // Create a signal with a gradual ramp in the middle
  std::vector<float> effective(total, -0.5f);
  
  // Add a ramp from position 100 to 200
  for (size_t i = 100; i < 200; ++i) {
    float progress = static_cast<float>(i - 100) / 100.0f;
    effective[i] = -0.5f + progress * 2.0f; // ramp from -0.5 to 1.5
  }
  
  // Fill the rest with high value
  for (size_t i = 200; i < total; ++i) {
    effective[i] = 1.5f;
  }
  
  std::vector<float> padded; buildPaddedSignal(effective, pad, pad, padded);
  
  std::vector<float> ring(41);
  FindFrontFrwd<float> finder(padded.data() + pad, total, ring);
  float pos = 0.0f; bool found = false;
  
  bool ok = finder(true, +1, 0.0f, 0.0f, 15.0f, 41.0f, 0.2f, pos, found);
  
  if (!ok || !found) {
    throw std::runtime_error("Gradual ramp front not detected");
  }
  
  // The front should be detected somewhere in the ramp region
  if (pos < 80.0f || pos > 220.0f) {
    std::ostringstream oss;
    oss << "Ramp front position out of expected range: " << pos;
    throw std::runtime_error(oss.str());
  }
  
  std::cout << "✓ gradual ramp fronts detection" << std::endl;
}

void test_multi_step_ramps() {
  const size_t total = 400, pad = 100;
  
  // Create a signal with multiple step-like ramps
  std::vector<float> effective(total, 0.0f);
  
  // First ramp: 0 to 1 from position 50-70
  for (size_t i = 50; i < 70; ++i) {
    effective[i] = static_cast<float>(i - 50) / 20.0f;
  }
  for (size_t i = 70; i < 150; ++i) effective[i] = 1.0f;
  
  // Second ramp: 1 to 2 from position 150-170
  for (size_t i = 150; i < 170; ++i) {
    effective[i] = 1.0f + static_cast<float>(i - 150) / 20.0f;
  }
  for (size_t i = 170; i < 250; ++i) effective[i] = 2.0f;
  
  // Third ramp: 2 to 3 from position 250-270
  for (size_t i = 250; i < 270; ++i) {
    effective[i] = 2.0f + static_cast<float>(i - 250) / 20.0f;
  }
  for (size_t i = 270; i < total; ++i) effective[i] = 3.0f;
  
  std::vector<float> padded; buildPaddedSignal(effective, pad, pad, padded);
  
  std::vector<float> ring(29);
  FindFrontFrwd<float> finder(padded.data() + pad, total, ring);
  float pos = 0.0f; bool found = false;
  
  bool ok = finder(true, +1, 0.0f, 0.0f, 11.0f, 29.0f, 0.15f, pos, found);
  
  if (!ok || !found) {
    throw std::runtime_error("Multi-step ramp front not detected");
  }
  
  // Should detect the first significant front
  if (pos < 30.0f || pos > 90.0f) {
    std::ostringstream oss;
    oss << "Multi-step ramp front position unexpected: " << pos;
    throw std::runtime_error(oss.str());
  }
  
  std::cout << "✓ multi-step ramps detection" << std::endl;
}

// ============================================================================
// NOISY SIGNAL FRONT DETECTION TESTS
// ============================================================================

void test_noisy_step_fronts() {
  const size_t total = 250, pad = 60;

  // Test front detection in presence of noise
  for (float noiseLevel : {0.05f, 0.1f, 0.2f}) {
    auto cleanSignal = makeStepFunction(total, 125, -1.0f, 1.0f);
    auto noisySignal = addNoise(cleanSignal, noiseLevel, 456);

    std::vector<float> padded; buildPaddedSignal(noisySignal, pad, pad, padded);

    std::vector<float> ring(21);
    FindFrontFrwd<float> finder(padded.data() + pad, total, ring);
    float pos = 0.0f; bool found = false;

    // Use higher averaging to reduce noise impact
    bool ok = finder(true, +1, 0.0f, 0.0f, 13.0f, 21.0f, 0.5f, pos, found);

    if (!ok) {
      std::ostringstream oss;
      oss << "Algorithm failed for noise level " << noiseLevel;
      throw std::runtime_error(oss.str());
    }

    // For moderate noise levels, should still detect the front
    if (noiseLevel <= 0.1f && !found) {
      std::ostringstream oss;
      oss << "Noisy step front not detected with noise level " << noiseLevel;
      throw std::runtime_error(oss.str());
    }
  }

  std::cout << "✓ noisy step fronts detection" << std::endl;
}

void test_signal_with_baseline_noise() {
  const size_t total = 200, pad = 50;

  // Create a signal with baseline noise and a clear step
  auto baseNoise = makeNoisySignal(total, 0.1f, 789);

  // Add a clear step at position 100
  for (size_t i = 100; i < total; ++i) {
    baseNoise[i] += 1.5f; // Add significant step above noise
  }

  std::vector<float> padded; buildPaddedSignal(baseNoise, pad, pad, padded);

  std::vector<float> ring(19);
  FindFrontFrwd<float> finder(padded.data() + pad, total, ring);
  float pos = 0.0f; bool found = false;

  bool ok = finder(true, +1, 0.0f, 0.0f, 9.0f, 19.0f, 0.3f, pos, found);

  if (!ok || !found) {
    throw std::runtime_error("Signal with baseline noise front not detected");
  }

  // Position should be near the step location
  if (std::abs(pos - 100.0f) > 25.0f) {
    std::ostringstream oss;
    oss << "Baseline noise signal position inaccurate: " << pos;
    throw std::runtime_error(oss.str());
  }

  std::cout << "✓ signal with baseline noise detection" << std::endl;
}

// ============================================================================
// FRONT TYPE AND DIRECTION TESTS
// ============================================================================

void test_front_type_specificity() {
  const size_t total = 150, pad = 40;

  // Test that front type parameter correctly filters detection
  auto risingSignal = makeStepFunction(total, 75, -0.8f, 0.8f);
  auto fallingSignal = makeStepFunction(total, 75, 0.8f, -0.8f);

  std::vector<float> risingPadded, fallingPadded;
  buildPaddedSignal(risingSignal, pad, pad, risingPadded);
  buildPaddedSignal(fallingSignal, pad, pad, fallingPadded);

  std::vector<float> ring(15);

  // Test rising front with correct type
  {
    FindFrontFrwd<float> finder(risingPadded.data() + pad, total, ring);
    float pos = 0.0f; bool found = false;
    bool ok = finder(true, +1, 0.0f, 0.0f, 5.0f, 15.0f, 0.3f, pos, found);

    if (!ok || !found) {
      throw std::runtime_error("Rising front not detected with frontType=+1");
    }
  }

  // Test rising front with wrong type (should not detect)
  {
    FindFrontFrwd<float> finder(risingPadded.data() + pad, total, ring);
    float pos = 0.0f; bool found = false;
    bool ok = finder(true, -1, 0.0f, 0.0f, 5.0f, 15.0f, 0.3f, pos, found);

    if (!ok || found) {
      throw std::runtime_error("Rising front incorrectly detected with frontType=-1");
    }
  }

  // Test falling front with correct type
  {
    FindFrontFrwd<float> finder(fallingPadded.data() + pad, total, ring);
    float pos = 0.0f; bool found = false;
    bool ok = finder(true, -1, 0.0f, 0.0f, 5.0f, 15.0f, 0.3f, pos, found);

    if (!ok || !found) {
      throw std::runtime_error("Falling front not detected with frontType=-1");
    }
  }

  // Test falling front with wrong type (should not detect)
  {
    FindFrontFrwd<float> finder(fallingPadded.data() + pad, total, ring);
    float pos = 0.0f; bool found = false;
    bool ok = finder(true, +1, 0.0f, 0.0f, 5.0f, 15.0f, 0.3f, pos, found);

    if (!ok || found) {
      throw std::runtime_error("Falling front incorrectly detected with frontType=+1");
    }
  }

  std::cout << "✓ front type specificity" << std::endl;
}

void test_bidirectional_front_detection() {
  const size_t total = 160, pad = 40;

  // Create signal with both rising and falling fronts
  std::vector<float> effective(total, 0.0f);

  // Rising front at position 50
  for (size_t i = 50; i < total; ++i) effective[i] = 1.0f;

  // Falling front at position 110
  for (size_t i = 110; i < total; ++i) effective[i] = -1.0f;

  std::vector<float> padded; buildPaddedSignal(effective, pad, pad, padded);

  std::vector<float> ring(13);

  // Test detection of rising front (frontType = 0 should detect any)
  {
    FindFrontFrwd<float> finder(padded.data() + pad, total, ring);
    float pos = 0.0f; bool found = false;
    bool ok = finder(true, 0, 0.0f, 0.0f, 3.0f, 13.0f, 0.2f, pos, found);

    if (!ok || !found) {
      throw std::runtime_error("Bidirectional detection failed");
    }

    // Should detect the first significant front (rising at ~50)
    if (pos < 30.0f || pos > 70.0f) {
      std::ostringstream oss;
      oss << "Bidirectional front position unexpected: " << pos;
      throw std::runtime_error(oss.str());
    }
  }

  std::cout << "✓ bidirectional front detection" << std::endl;
}

int main() {
  std::cout << "FindFrontFrwd Front Detection Test Suite" << std::endl;
  std::cout << "========================================" << std::endl;

  // Run step function tests
  RUN_FRONT_TEST(test_sharp_step_fronts);
  RUN_FRONT_TEST(test_small_amplitude_steps);
  RUN_FRONT_TEST(test_negative_step_fronts);

  // Run ramp function tests
  RUN_FRONT_TEST(test_gradual_ramp_fronts);
  RUN_FRONT_TEST(test_multi_step_ramps);

  // Run noisy signal tests
  RUN_FRONT_TEST(test_noisy_step_fronts);
  RUN_FRONT_TEST(test_signal_with_baseline_noise);

  // Run front type tests
  RUN_FRONT_TEST(test_front_type_specificity);
  RUN_FRONT_TEST(test_bidirectional_front_detection);

  // Print results summary
  std::cout << "\n" << std::string(50, '=') << std::endl;
  std::cout << "FRONT DETECTION TEST RESULTS" << std::endl;
  std::cout << std::string(50, '=') << std::endl;

  size_t passed = 0, failed = 0;
  for (const auto& result : frontResults) {
    if (result.passed) {
      std::cout << "✓ " << result.testName << std::endl;
      ++passed;
    } else {
      std::cout << "❌ " << result.testName << ": " << result.error << std::endl;
      ++failed;
    }
  }

  std::cout << "\nSUMMARY: " << passed << " passed, " << failed << " failed" << std::endl;

  if (failed == 0) {
    std::cout << "\n🎉 All front detection tests PASSED!" << std::endl;
    return 0;
  } else {
    std::cout << "\n❌ " << failed << " front detection test(s) FAILED!" << std::endl;
    return 1;
  }
}
