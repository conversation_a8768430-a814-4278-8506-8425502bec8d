# Build artifacts
*.o
*.obj
*.so
*.dylib
*.dll
*.a
*.lib

# Test executables
find_front_frwd_tests
find_front_frwd_tests.exe

# Build directory
build/

# Temporary build files
*.tmp
*.temp
*~
.DS_Store

# IDE and editor files
.vscode/
.idea/
*.swp
*.swo
*.bak

# Profiling and debugging files
*.gcda
*.gcno
*.gcov
gmon.out
callgrind.out.*
massif.out.*

# Static analysis output
*.plist

# Backup files
*.orig
*.rej

# Log files
*.log

# Core dumps
core
core.*

# Memory check output
valgrind-*.log
valgrind-*.xml

# Performance test outputs
perf_results_*.txt
benchmark_*.csv

