# Makefile for Signal-Processing module tests

CXX ?= g++
CXXFLAGS ?= -std=c++17 -Wall -Wextra -O2 -g -pedantic
INCLUDES = -I.. -I../.. -I../../chunk-processor -I../../iiq-stream -I../../stream-pipeline

TEST_SOURCES = find_front_frwd.cpp tests/test_find_front_frwd.cpp
TEST_EXECUTABLE = find_front_frwd_tests

all: test

$(TEST_EXECUTABLE): $(TEST_SOURCES)
	$(CXX) $(CXXFLAGS) $(INCLUDES) -o $@ $^

build: $(TEST_EXECUTABLE)

test: $(TEST_EXECUTABLE)
	./$(TEST_EXECUTABLE)

clean:
	rm -f $(TEST_EXECUTABLE) tests/*.o *.o

.PHONY: all build test clean

